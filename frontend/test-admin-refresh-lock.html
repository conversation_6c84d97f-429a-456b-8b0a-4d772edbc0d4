<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Refresh Lock Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        button.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.locked { background: #f8d7da; color: #721c24; }
        .status.unlocked { background: #d4edda; color: #155724; }
        .status.unknown { background: #e2e3e5; color: #383d41; }
        .test-steps {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Admin Refresh Lock Test</h1>
    <p>This test verifies the specific issue: when locked on <code>/admin</code> and refreshing, user should stay on <code>/admin</code> instead of being redirected to <code>/admin/login</code>.</p>

    <div class="test-section">
        <h2>Test Scenario</h2>
        <div class="test-steps">
            <ol>
                <li>Navigate to <code>/admin</code> page</li>
                <li>Login as admin user</li>
                <li>Lock the session (manually or via timeout)</li>
                <li>Refresh the page (F5 or Ctrl+R)</li>
                <li><strong>Expected:</strong> Stay on <code>/admin</code> with session lock modal</li>
                <li><strong>Bug:</strong> Redirected to <code>/admin/login?redirect=/admin</code></li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>Current Status</h2>
        <p>
            <strong>Current URL:</strong> <span id="currentUrl">-</span>
        </p>
        <p>
            <strong>Expected URL:</strong> <code>http://localhost:5173/admin</code>
        </p>
        <p>
            <strong>Test Status:</strong> 
            <span id="testStatus" class="status unknown">Not Started</span>
        </p>
    </div>

    <div class="test-section">
        <h2>Test Actions</h2>
        <button onclick="navigateToAdmin()">1. Navigate to /admin</button>
        <button onclick="checkLoginStatus()">2. Check Login Status</button>
        <button onclick="simulateLock()">3. Simulate Session Lock</button>
        <button onclick="simulateRefresh()">4. Simulate Refresh</button>
        <button onclick="runFullTest()" class="success">Run Full Test</button>
        <button onclick="clearLog()" class="danger">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Manual Test Instructions</h2>
        <div class="test-steps">
            <ol>
                <li>Open <a href="http://localhost:5173/admin" target="_blank">http://localhost:5173/admin</a> in a new tab</li>
                <li>Login with admin credentials</li>
                <li>Use the "Test Lock Session" button or wait for timeout</li>
                <li>Refresh the page (F5)</li>
                <li>Check if URL stays <code>/admin</code> or redirects to <code>/admin/login</code></li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log" class="log"></div>
    </div>

    <button onclick="playSound()">Play Sound</button>


    <script>

  window.addEventListener('load', () => {
    const audio = new Audio('sound.mp3');
    audio.play().catch(err => {
      console.warn('Autoplay failed:', err);
    });
  });

        // Logging function
        function log(level, message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Status update functions
        function updateCurrentUrl() {
            document.getElementById('currentUrl').textContent = window.location.href;
        }

        function updateTestStatus(status, className = 'unknown') {
            const statusEl = document.getElementById('testStatus');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        // Test functions
        function navigateToAdmin() {
            log('info', 'Step 1: Navigating to /admin...');
            window.open('http://localhost:5173/admin', '_blank');
            log('success', 'Opened /admin in new tab');
        }

        function checkLoginStatus() {
            log('info', 'Step 2: Checking login status...');
            log('info', 'Manual step: Check if you are logged in as admin');
            log('info', 'If not logged in, use admin credentials to login');
        }

        function simulateLock() {
            log('warning', 'Step 3: Simulating session lock...');
            log('info', 'Manual step: Use "Test Lock Session" button in admin dashboard');
            log('info', 'Or wait for session timeout (15 minutes of inactivity)');
            log('info', 'Verify that session lock modal appears');
        }

        function simulateRefresh() {
            log('info', 'Step 4: Simulating page refresh...');
            log('warning', 'Manual step: Press F5 or Ctrl+R to refresh the page');
            log('info', 'Check the URL after refresh:');
            log('info', '✅ PASS: URL stays http://localhost:5173/admin');
            log('error', '❌ FAIL: URL changes to http://localhost:5173/admin/login?redirect=/admin');
        }

        function runFullTest() {
            log('info', '=== Running Full Test Sequence ===');
            updateTestStatus('Running', 'warning');
            
            setTimeout(() => {
                navigateToAdmin();
                setTimeout(() => {
                    checkLoginStatus();
                    setTimeout(() => {
                        simulateLock();
                        setTimeout(() => {
                            simulateRefresh();
                            updateTestStatus('Manual Verification Required', 'warning');
                            log('success', 'Test sequence completed - manual verification required');
                        }, 2000);
                    }, 2000);
                }, 2000);
            }, 1000);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            updateTestStatus('Not Started', 'unknown');
            log('info', 'Test log cleared');
        }

        // Automated checks
        function checkUrlPattern() {
            const currentUrl = window.location.href;
            updateCurrentUrl();
            
            if (currentUrl.includes('/admin/login')) {
                log('error', '❌ DETECTED: Redirected to admin login page');
                updateTestStatus('FAIL - Redirected', 'locked');
                return false;
            } else if (currentUrl.includes('/admin')) {
                log('success', '✅ GOOD: Still on admin page');
                updateTestStatus('PASS - Stayed on Admin', 'unlocked');
                return true;
            } else {
                log('info', 'ℹ️ Not on admin page');
                updateTestStatus('Not on Admin Page', 'unknown');
                return null;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('info', 'Admin Refresh Lock Test initialized');
            updateCurrentUrl();
            checkUrlPattern();
            
            // Check URL periodically
            setInterval(() => {
                updateCurrentUrl();
                checkUrlPattern();
            }, 2000);
        });

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                log('info', 'Page became visible - checking URL');
                updateCurrentUrl();
                checkUrlPattern();
            }
        });

        // Listen for URL changes (if using SPA routing)
        window.addEventListener('popstate', function() {
            log('info', 'URL changed - checking pattern');
            updateCurrentUrl();
            checkUrlPattern();
        });
    </script>
</body>
</html>
