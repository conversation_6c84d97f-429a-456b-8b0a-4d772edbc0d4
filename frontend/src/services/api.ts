import axios, { type AxiosInstance, type AxiosResponse, type AxiosError } from 'axios'
import { useAuthStore } from '@/stores/auth'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
const API_VERSION = 'v1'

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    type?: string
    code?: string
  }
  message?: string
  timestamp?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// API Error Class
export class ApiError extends Error {
  public status: number
  public code?: string
  public type?: string

  constructor(message: string, status: number, code?: string, type?: string) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.code = code
    this.type = type
  }
}

// Create Axios Instance
class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request Interceptor - Add auth token and debug logging
    this.api.interceptors.request.use(
      (config) => {
        // Debug logging for login requests
        if (config.url?.includes('/auth/login')) {
          console.log('🔍 [API DEBUG] Login request:', {
            url: config.url,
            baseURL: config.baseURL,
            fullURL: `${config.baseURL}${config.url}`,
            method: config.method,
            data: config.data,
            headers: config.headers
          })
        }

        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
      },
      (error) => {
        console.error('🔍 [API DEBUG] Request error:', error)
        return Promise.reject(error)
      }
    )

    // Response Interceptor - Handle errors globally
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // Debug logging for login responses
        if (response.config.url?.includes('/auth/login')) {
          console.log('🔍 [API DEBUG] Login response:', {
            status: response.status,
            statusText: response.statusText,
            data: response.data,
            headers: response.headers
          })
        }
        return response
      },
      (error: AxiosError<ApiResponse>) => {
        // Debug logging for login errors
        if (error.config?.url?.includes('/auth/login')) {
          console.error('🔍 [API DEBUG] Login error:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message,
            code: error.code,
            config: {
              url: error.config?.url,
              baseURL: error.config?.baseURL,
              method: error.config?.method,
              data: error.config?.data
            }
          })
        }

        const authStore = useAuthStore()

        // Handle 423 - Session Locked
        if (error.response?.status === 423) {
          console.log('🔒 API call blocked - session is locked')
          // Import session service to handle the lock response
          import('./session').then(({ sessionService }) => {
            sessionService.handleSessionLockResponse(error)
          })
          // Don't clear tokens or logout, just pass through the error
          throw new ApiError('Session is locked', 423)
        }

        // Handle 401 - Unauthorized
        if (error.response?.status === 401) {
          const requestUrl = error.config?.url || ''

          // Don't logout for authentication endpoints (login, register, etc.)
          // Only logout for protected endpoints when session is expired
          const isAuthEndpoint = requestUrl.includes('/auth/login') ||
                                 requestUrl.includes('/auth/register') ||
                                 requestUrl.includes('/auth/refresh') ||
                                 requestUrl.includes('/auth/forgot-password') ||
                                 requestUrl.includes('/auth/reset-password')

          if (!isAuthEndpoint) {
            // This is a session expiry on a protected endpoint
            console.log('🔒 Session expired - logging out user')
            authStore.logout()
            window.location.href = '/login'
            return Promise.reject(new ApiError('Session expired', 401))
          }

          // For auth endpoints, just pass through the error without logout
          console.log('❌ Authentication failed on auth endpoint:', requestUrl)
        }

        // Handle API errors
        if (error.response?.data) {
          const { error: apiError } = error.response.data
          throw new ApiError(
            apiError?.message || 'An error occurred',
            error.response.status,
            apiError?.code,
            apiError?.type
          )
        }

        // Handle network errors
        if (error.code === 'NETWORK_ERROR' || !error.response) {
          throw new ApiError('Network error. Please check your connection.', 0)
        }

        // Generic error
        throw new ApiError('An unexpected error occurred', error.response?.status || 500)
      }
    )
  }

  // Generic HTTP Methods
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.api.get<ApiResponse<T>>(url, { params })
    return response.data.data as T
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.post<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async patch<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.patch<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<ApiResponse<T>>(url)
    return response.data.data as T
  }

  // Get raw response (for cases where you need full response data)
  async getRaw<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    const response = await this.api.get<ApiResponse<T>>(url, { params })
    return response.data
  }

  async postRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.post<ApiResponse<T>>(url, data)
    return response.data
  }

  async putRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.put<ApiResponse<T>>(url, data)
    return response.data
  }

  async patchRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.patch<ApiResponse<T>>(url, data)
    return response.data
  }

  async deleteRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.delete<ApiResponse<T>>(url, { data })
    return response.data
  }
}

// Export singleton instance
export const apiService = new ApiService()
export default apiService
