/**
 * Performance monitoring utility to track CPU usage, memory consumption,
 * and identify performance bottlenecks
 */

interface PerformanceMetrics {
  timestamp: number
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  activeIntervals: number
  activeTimeouts: number
  socketConnections: number
  domNodes: number
  eventListeners: number
}

interface IntervalInfo {
  id: number
  callback: string
  delay: number
  createdAt: number
  lastRun: number
  runCount: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private intervals = new Map<number, IntervalInfo>()
  private timeouts = new Map<number, any>()
  private isMonitoring = false
  private monitoringInterval?: number
  private originalSetInterval: typeof setInterval
  private originalSetTimeout: typeof setTimeout
  private originalClearInterval: typeof clearInterval
  private originalClearTimeout: typeof clearTimeout

  constructor() {
    this.originalSetInterval = window.setInterval
    this.originalSetTimeout = window.setTimeout
    this.originalClearInterval = window.clearInterval
    this.originalClearTimeout = window.clearTimeout
    
    this.setupInterceptors()
  }

  /**
   * Intercept setInterval/setTimeout to track active timers
   */
  private setupInterceptors() {
    const self = this

    // Intercept setInterval
    window.setInterval = function(callback: any, delay: number, ...args: any[]) {
      const id = self.originalSetInterval.call(window, callback, delay, ...args)
      
      self.intervals.set(id, {
        id,
        callback: callback.toString().substring(0, 100),
        delay,
        createdAt: Date.now(),
        lastRun: Date.now(),
        runCount: 0
      })
      
      console.log(`🔄 New interval created: ID ${id}, delay: ${delay}ms`)
      return id
    }

    // Intercept setTimeout
    window.setTimeout = function(callback: any, delay: number, ...args: any[]) {
      const id = self.originalSetTimeout.call(window, callback, delay, ...args)
      
      self.timeouts.set(id, {
        id,
        callback: callback.toString().substring(0, 100),
        delay,
        createdAt: Date.now()
      })
      
      return id
    }

    // Intercept clearInterval
    window.clearInterval = function(id: number) {
      self.intervals.delete(id)
      console.log(`🛑 Interval cleared: ID ${id}`)
      return self.originalClearInterval.call(window, id)
    }

    // Intercept clearTimeout
    window.clearTimeout = function(id: number) {
      self.timeouts.delete(id)
      return self.originalClearTimeout.call(window, id)
    }
  }

  /**
   * Start monitoring performance
   */
  startMonitoring(intervalMs: number = 5000) {
    if (this.isMonitoring) return

    this.isMonitoring = true
    console.log('📊 Performance monitoring started')

    this.monitoringInterval = this.originalSetInterval(() => {
      this.collectMetrics()
    }, intervalMs)
  }

  /**
   * Stop monitoring performance
   */
  stopMonitoring() {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    if (this.monitoringInterval) {
      this.originalClearInterval(this.monitoringInterval)
      this.monitoringInterval = undefined
    }
    console.log('📊 Performance monitoring stopped')
  }

  /**
   * Collect current performance metrics
   */
  private collectMetrics() {
    const memory = (performance as any).memory
    const memoryUsage = memory ? {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
    } : { used: 0, total: 0, percentage: 0 }

    const metrics: PerformanceMetrics = {
      timestamp: Date.now(),
      memoryUsage,
      activeIntervals: this.intervals.size,
      activeTimeouts: this.timeouts.size,
      socketConnections: this.getSocketConnectionCount(),
      domNodes: document.querySelectorAll('*').length,
      eventListeners: this.getEventListenerCount()
    }

    this.metrics.push(metrics)

    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics.shift()
    }

    // Log warnings for high resource usage
    this.checkForWarnings(metrics)
  }

  /**
   * Check for performance warnings
   */
  private checkForWarnings(metrics: PerformanceMetrics) {
    if (metrics.activeIntervals > 10) {
      console.warn(`⚠️ High number of active intervals: ${metrics.activeIntervals}`)
    }

    if (metrics.memoryUsage.percentage > 80) {
      console.warn(`⚠️ High memory usage: ${metrics.memoryUsage.percentage}%`)
    }

    if (metrics.domNodes > 5000) {
      console.warn(`⚠️ High DOM node count: ${metrics.domNodes}`)
    }
  }

  /**
   * Get Socket.io connection count
   */
  private getSocketConnectionCount(): number {
    // Try to access global socket instances
    const globalSocket = (window as any).globalSocket
    if (globalSocket?.socket?.value?.connected) {
      return 1
    }
    return 0
  }

  /**
   * Estimate event listener count
   */
  private getEventListenerCount(): number {
    // This is an approximation - exact count is not easily accessible
    return document.querySelectorAll('[onclick], [onload], [onchange]').length
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics]
  }

  /**
   * Get active intervals info
   */
  getActiveIntervals(): IntervalInfo[] {
    return Array.from(this.intervals.values())
  }

  /**
   * Get active timeouts info
   */
  getActiveTimeouts(): any[] {
    return Array.from(this.timeouts.values())
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const latest = this.metrics[this.metrics.length - 1]
    if (!latest) return null

    return {
      memoryUsage: `${latest.memoryUsage.percentage}% (${Math.round(latest.memoryUsage.used / 1024 / 1024)}MB)`,
      activeIntervals: latest.activeIntervals,
      activeTimeouts: latest.activeTimeouts,
      socketConnections: latest.socketConnections,
      domNodes: latest.domNodes,
      intervalDetails: this.getActiveIntervals().map(interval => ({
        id: interval.id,
        delay: interval.delay,
        runCount: interval.runCount,
        age: Math.round((Date.now() - interval.createdAt) / 1000)
      }))
    }
  }

  /**
   * Clear all tracked intervals (emergency cleanup)
   */
  clearAllIntervals() {
    console.warn('🚨 Emergency cleanup: clearing all tracked intervals')
    for (const id of this.intervals.keys()) {
      this.originalClearInterval(id)
    }
    this.intervals.clear()
  }

  /**
   * Clear all tracked timeouts
   */
  clearAllTimeouts() {
    console.warn('🚨 Emergency cleanup: clearing all tracked timeouts')
    for (const id of this.timeouts.keys()) {
      this.originalClearTimeout(id)
    }
    this.timeouts.clear()
  }
}

// Create global instance
export const performanceMonitor = new PerformanceMonitor()

// Auto-start monitoring in development
if (import.meta.env.DEV) {
  performanceMonitor.startMonitoring()
}

// Expose to window for debugging
if (typeof window !== 'undefined') {
  (window as any).performanceMonitor = performanceMonitor
}

export default performanceMonitor
