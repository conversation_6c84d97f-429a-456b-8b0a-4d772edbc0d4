<template>
  <div class="notification-manager">
    <!-- Notification Settings Card -->
    <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
      <div class="card-body p-6">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-base-content">Push Notifications</h3>
              <p class="text-sm text-base-content/60">Manage your notification preferences</p>
            </div>
          </div>
          
          <!-- Status Indicator -->
          <div class="flex items-center space-x-2">
            <div 
              :class="[
                'w-3 h-3 rounded-full',
                isSubscribed ? 'bg-success animate-pulse' : 'bg-error'
              ]"
            ></div>
            <span class="text-sm font-medium" :class="isSubscribed ? 'text-success' : 'text-error'">
              {{ statusMessage }}
            </span>
          </div>
        </div>

        <!-- Support Check -->
        <div v-if="!permission.supported" class="alert alert-warning mb-4">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
          </svg>
          <span>Push notifications are not supported in this browser.</span>
        </div>

        <!-- Permission Denied -->
        <div v-else-if="isDenied" class="alert alert-error mb-4">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <div>
            <div class="font-semibold">Notifications Blocked</div>
            <div class="text-sm">Please enable notifications in your browser settings to receive updates.</div>
          </div>
        </div>

        <!-- Controls -->
        <div class="space-y-4">
          <!-- Permission Request -->
          <div v-if="canRequestPermission" class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
            <div>
              <div class="font-medium">Enable Notifications</div>
              <div class="text-sm text-base-content/70">Get notified about important energy updates and alerts</div>
            </div>
            <button 
              @click="requestPermission" 
              :disabled="isSubscribing"
              class="btn btn-primary btn-sm"
            >
              <span v-if="isSubscribing" class="loading loading-spinner loading-sm"></span>
              {{ isSubscribing ? 'Requesting...' : 'Enable' }}
            </button>
          </div>

          <!-- Subscription Controls -->
          <div v-if="isGranted" class="space-y-3">
            <div class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
              <div>
                <div class="font-medium">Push Notifications</div>
                <div class="text-sm text-base-content/70">
                  {{ isSubscribed ? 'You will receive push notifications' : 'Subscribe to receive notifications' }}
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button 
                  v-if="!isSubscribed"
                  @click="subscribe" 
                  :disabled="isSubscribing"
                  class="btn btn-success btn-sm"
                >
                  <span v-if="isSubscribing" class="loading loading-spinner loading-sm"></span>
                  {{ isSubscribing ? 'Subscribing...' : 'Subscribe' }}
                </button>
                <button 
                  v-else
                  @click="unsubscribe" 
                  :disabled="isSubscribing"
                  class="btn btn-error btn-sm"
                >
                  <span v-if="isSubscribing" class="loading loading-spinner loading-sm"></span>
                  {{ isSubscribing ? 'Unsubscribing...' : 'Unsubscribe' }}
                </button>
              </div>
            </div>

            <!-- Test Notification -->
            <div v-if="isSubscribed" class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
              <div>
                <div class="font-medium">Test Notification</div>
                <div class="text-sm text-base-content/70">Send a test notification to verify everything works</div>
              </div>
              <button
                @click="sendTest"
                :disabled="isSendingTest"
                class="btn btn-accent btn-sm"
              >
                <span v-if="isSendingTest" class="loading loading-spinner loading-sm"></span>
                {{ isSendingTest ? 'Sending...' : 'Send Test' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="alert alert-error mt-4">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Subscription Info -->
        <div v-if="isSubscribed && subscription" class="mt-6 p-4 bg-base-200/30 rounded-lg">
          <div class="text-sm font-medium text-base-content/70 mb-2">Subscription Details</div>
          <div class="text-xs text-base-content/50 break-all">
            Endpoint: {{ subscription.endpoint.substring(0, 50) }}...
          </div>
        </div>
      </div>
    </div>

    <!-- Notification Types -->
    <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl mt-6">
      <div class="card-body p-6">
        <h3 class="text-lg font-semibold text-base-content mb-4">Notification Types</h3>
        
        <div class="space-y-3">
          <div v-for="type in notificationTypes" :key="type.id" 
               class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 rounded-lg flex items-center justify-center"
                   :class="type.colorClass">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path :d="type.iconPath" />
                </svg>
              </div>
              <div>
                <div class="font-medium">{{ type.name }}</div>
                <div class="text-sm text-base-content/70">{{ type.description }}</div>
              </div>
            </div>
            <input 
              type="checkbox" 
              :checked="type.enabled" 
              @change="toggleNotificationType(type.id)"
              class="checkbox checkbox-primary"
              :disabled="!isSubscribed"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { usePushNotifications } from '@/composables/usePushNotifications'

// Use push notifications composable
const {
  permission,
  subscription,
  isSubscribing,
  error,
  canRequestPermission,
  isGranted,
  isDenied,
  isSubscribed,
  requestPermission,
  subscribe,
  unsubscribe,
  showNotification,
  sendTestNotification
} = usePushNotifications()

// Local state
const isSendingTest = ref(false)

// Notification types configuration
const notificationTypes = ref([
  {
    id: 'energy-alerts',
    name: 'Energy Alerts',
    description: 'High consumption warnings and efficiency tips',
    enabled: true,
    colorClass: 'bg-warning/20 text-warning',
    iconPath: 'M13 10V3L4 14h7v7l9-11h-7z'
  },
  {
    id: 'system-updates',
    name: 'System Updates',
    description: 'Platform updates and maintenance notifications',
    enabled: true,
    colorClass: 'bg-info/20 text-info',
    iconPath: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
  },
  {
    id: 'lead-notifications',
    name: 'New Leads',
    description: 'Notifications when new leads are generated',
    enabled: true,
    colorClass: 'bg-success/20 text-success',
    iconPath: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
  },
  {
    id: 'reports',
    name: 'Reports Ready',
    description: 'When energy reports and analytics are available',
    enabled: false,
    colorClass: 'bg-primary/20 text-primary',
    iconPath: 'M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  }
])

// Computed
const statusMessage = computed(() => {
  if (!permission.value.supported) return 'Not Supported'
  if (isDenied.value) return 'Blocked'
  if (isSubscribed.value) return 'Active'
  if (isGranted.value) return 'Ready'
  return 'Disabled'
})

// Methods
const sendTest = async () => {
  try {
    isSendingTest.value = true

    await sendTestNotification('This is a test notification from your energy management dashboard! 🔋')

    console.log('✅ Test notification sent successfully')
  } catch (err) {
    console.error('❌ Failed to send test notification:', err)
  } finally {
    isSendingTest.value = false
  }
}

const toggleNotificationType = (typeId: string) => {
  const type = notificationTypes.value.find(t => t.id === typeId)
  if (type) {
    type.enabled = !type.enabled
    console.log(`Notification type ${typeId} ${type.enabled ? 'enabled' : 'disabled'}`)
    
    // Save preferences to localStorage
    const preferences = notificationTypes.value.reduce((acc, t) => {
      acc[t.id] = t.enabled
      return acc
    }, {} as Record<string, boolean>)
    
    localStorage.setItem('notification-preferences', JSON.stringify(preferences))
  }
}

// Load saved preferences
const loadPreferences = () => {
  try {
    const saved = localStorage.getItem('notification-preferences')
    if (saved) {
      const preferences = JSON.parse(saved)
      notificationTypes.value.forEach(type => {
        if (preferences.hasOwnProperty(type.id)) {
          type.enabled = preferences[type.id]
        }
      })
    }
  } catch (err) {
    console.error('Failed to load notification preferences:', err)
  }
}

// Initialize
loadPreferences()
</script>

<style scoped>
.glass-effect {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.checkbox:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.alert {
  border-radius: 0.75rem;
}
</style>
