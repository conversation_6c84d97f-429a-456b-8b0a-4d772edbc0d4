/**
 * Composable for managing background tasks with automatic pause/resume
 * based on page visibility to reduce CPU usage when page is hidden
 */

import { ref, onMounted, onUnmounted } from 'vue'

interface BackgroundTask {
  id: string
  callback: () => void | Promise<void>
  interval: number
  intervalId?: number
  isActive: boolean
  lastRun: number
  runCount: number
  pauseWhenHidden: boolean
}

export function useBackgroundTasks() {
  const tasks = ref(new Map<string, BackgroundTask>())
  const isPageVisible = ref(!document.hidden)
  const isPaused = ref(false)

  /**
   * Register a background task
   */
  const registerTask = (
    id: string,
    callback: () => void | Promise<void>,
    interval: number,
    options: {
      pauseWhenHidden?: boolean
      startImmediately?: boolean
    } = {}
  ) => {
    const { pauseWhenHidden = true, startImmediately = true } = options

    // Clear existing task if it exists
    if (tasks.value.has(id)) {
      unregisterTask(id)
    }

    const task: BackgroundTask = {
      id,
      callback,
      interval,
      isActive: false,
      lastRun: 0,
      runCount: 0,
      pauseWhenHidden
    }

    tasks.value.set(id, task)

    if (startImmediately) {
      startTask(id)
    }

    console.log(`📋 Background task registered: ${id} (${interval}ms)`)
    return id
  }

  /**
   * Start a specific task
   */
  const startTask = (id: string) => {
    const task = tasks.value.get(id)
    if (!task || task.isActive) return

    // Don't start if page is hidden and task should pause
    if (!isPageVisible.value && task.pauseWhenHidden && !isPaused.value) {
      console.log(`⏸️ Task ${id} not started - page is hidden`)
      return
    }

    const wrappedCallback = async () => {
      try {
        task.lastRun = Date.now()
        task.runCount++
        await task.callback()
      } catch (error) {
        console.error(`❌ Background task ${id} failed:`, error)
      }
    }

    // TEMPORARILY DISABLED TO FIX CPU USAGE
    // task.intervalId = setInterval(wrappedCallback, task.interval)
    // task.isActive = true

    console.log(`▶️ Background task disabled to fix CPU: ${id}`)
  }

  /**
   * Stop a specific task
   */
  const stopTask = (id: string) => {
    const task = tasks.value.get(id)
    if (!task || !task.isActive) return

    if (task.intervalId) {
      clearInterval(task.intervalId)
      task.intervalId = undefined
    }
    task.isActive = false

    console.log(`⏹️ Background task stopped: ${id}`)
  }

  /**
   * Unregister a task completely
   */
  const unregisterTask = (id: string) => {
    stopTask(id)
    tasks.value.delete(id)
    console.log(`🗑️ Background task unregistered: ${id}`)
  }

  /**
   * Pause all tasks
   */
  const pauseAllTasks = () => {
    if (isPaused.value) return

    isPaused.value = true
    for (const [id, task] of tasks.value) {
      if (task.isActive) {
        stopTask(id)
      }
    }
    console.log('⏸️ All background tasks paused')
  }

  /**
   * Resume all tasks
   */
  const resumeAllTasks = () => {
    if (!isPaused.value) return

    isPaused.value = false
    for (const [id, task] of tasks.value) {
      if (!task.isActive && (isPageVisible.value || !task.pauseWhenHidden)) {
        startTask(id)
      }
    }
    console.log('▶️ All background tasks resumed')
  }

  /**
   * Handle page visibility changes
   */
  const handleVisibilityChange = () => {
    const wasVisible = isPageVisible.value
    isPageVisible.value = !document.hidden

    if (isPageVisible.value && !wasVisible) {
      // Page became visible
      console.log('👁️ Page became visible - resuming background tasks')
      for (const [id, task] of tasks.value) {
        if (!task.isActive && task.pauseWhenHidden && !isPaused.value) {
          startTask(id)
        }
      }
    } else if (!isPageVisible.value && wasVisible) {
      // Page became hidden
      console.log('🙈 Page became hidden - pausing background tasks')
      for (const [id, task] of tasks.value) {
        if (task.isActive && task.pauseWhenHidden) {
          stopTask(id)
        }
      }
    }
  }

  /**
   * Get task statistics
   */
  const getTaskStats = () => {
    const stats = {
      total: tasks.value.size,
      active: 0,
      paused: 0,
      hidden: 0,
      tasks: [] as any[]
    }

    for (const [id, task] of tasks.value) {
      if (task.isActive) {
        stats.active++
      } else if (!isPageVisible.value && task.pauseWhenHidden) {
        stats.hidden++
      } else {
        stats.paused++
      }

      stats.tasks.push({
        id,
        interval: task.interval,
        isActive: task.isActive,
        runCount: task.runCount,
        lastRun: task.lastRun ? new Date(task.lastRun).toLocaleTimeString() : 'Never',
        pauseWhenHidden: task.pauseWhenHidden
      })
    }

    return stats
  }

  /**
   * Emergency cleanup - stop all tasks
   */
  const emergencyCleanup = () => {
    console.warn('🚨 Emergency cleanup: stopping all background tasks')
    for (const id of tasks.value.keys()) {
      unregisterTask(id)
    }
  }

  // Setup lifecycle hooks
  onMounted(() => {
    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Listen for page unload to cleanup
    window.addEventListener('beforeunload', emergencyCleanup)

    console.log('📋 Background task manager initialized')
  })

  onUnmounted(() => {
    // Cleanup all tasks
    emergencyCleanup()

    // Remove event listeners
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('beforeunload', emergencyCleanup)

    console.log('📋 Background task manager cleaned up')
  })

  return {
    // State
    isPageVisible,
    isPaused,
    
    // Methods
    registerTask,
    startTask,
    stopTask,
    unregisterTask,
    pauseAllTasks,
    resumeAllTasks,
    getTaskStats,
    emergencyCleanup
  }
}

// Create a global instance for shared use
let globalBackgroundTasks: ReturnType<typeof useBackgroundTasks> | null = null

export function getGlobalBackgroundTasks() {
  if (!globalBackgroundTasks) {
    globalBackgroundTasks = useBackgroundTasks()
  }
  return globalBackgroundTasks
}

export default useBackgroundTasks
