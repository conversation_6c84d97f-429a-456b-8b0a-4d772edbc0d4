// Vue composable for Firebase Analytics
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { useAuthStore } from '@/stores/auth'
import {
  trackEvent,
  trackPageView,
  trackUserAction,
  setAnalyticsUser,
  trackEnergyConsultation,
  trackContactForm,
  trackServiceInterest,
  trackQuoteRequest,
  trackUserEngagement
} from '@/plugins/firebase'

export function useAnalytics() {
  // Safely get router and route if available
  const instance = getCurrentInstance()
  const router = instance?.appContext?.app?.config?.globalProperties?.$router
  const route = instance?.proxy?.$route
  const authStore = useAuthStore()
  
  const pageStartTime = ref(Date.now())
  const isTracking = ref(true)

  // Track page views automatically
  const trackCurrentPage = () => {
    const pageName = route?.name || route?.path || window.location.pathname
    const pageTitle = document.title
    trackPageView(pageName, pageTitle)
  }

  // Track page time when leaving
  const trackPageTime = () => {
    const timeSpent = Math.round((Date.now() - pageStartTime.value) / 1000)
    if (timeSpent > 5) { // Only track if user spent more than 5 seconds
      trackEvent('page_time', {
        page: route?.path || window.location.pathname,
        time_seconds: timeSpent,
        page_name: route?.name || 'unknown'
      })
    }
  }

  // Set user properties when authenticated
  const updateUserProperties = () => {
    if (authStore.isAuthenticated && authStore.user) {
      setAnalyticsUser(authStore.user.id.toString(), {
        user_role: authStore.user.role,
        user_type: authStore.user.role === 'admin' ? 'admin' : 'customer',
        registration_date: authStore.user.created_at
      })
    }
  }

  // Business-specific tracking methods
  const analytics = {
    // Page tracking
    trackPage: trackCurrentPage,
    
    // User actions
    trackClick: (element, category = 'ui') => {
      trackUserAction('click', category, element)
    },
    
    trackDownload: (fileName, fileType = '') => {
      trackEvent('file_download', {
        file_name: fileName,
        file_type: fileType,
        page: route?.path || window.location.pathname
      })
    },
    
    // Energy consultation specific
    trackConsultationRequest: (type, service) => {
      trackEnergyConsultation(type, service)
    },
    
    trackContactSubmission: (formType, source = route?.path || window.location.pathname) => {
      trackContactForm(formType, source)
    },
    
    trackServiceView: (serviceName) => {
      const timeOnPage = Math.round((Date.now() - pageStartTime.value) / 1000)
      trackServiceInterest(serviceName, timeOnPage)
    },
    
    trackQuote: (serviceType, value = 0) => {
      trackQuoteRequest(serviceType, value)
    },
    
    // User engagement
    trackEngagement: (action, element = '') => {
      trackUserEngagement(action, element)
    },
    
    trackSearch: (searchTerm, results = 0) => {
      trackEvent('search', {
        search_term: searchTerm,
        results_count: results,
        page: route?.path || window.location.pathname
      })
    },

    trackError: (errorType, errorMessage = '') => {
      trackEvent('error', {
        error_type: errorType,
        error_message: errorMessage,
        page: route?.path || window.location.pathname,
        user_agent: navigator.userAgent
      })
    },
    
    // Business metrics
    trackLeadGeneration: (source, medium = '', campaign = '') => {
      trackEvent('lead_generation', {
        source: source,
        medium: medium,
        campaign: campaign,
        page: route?.path || window.location.pathname
      })
    },

    trackConversion: (conversionType, value = 0) => {
      trackEvent('conversion', {
        conversion_type: conversionType,
        value: value,
        currency: 'EUR',
        page: route?.path || window.location.pathname
      })
    }
  }

  // Lifecycle hooks
  onMounted(() => {
    pageStartTime.value = Date.now()
    trackCurrentPage()
    updateUserProperties()
    
    // Track user authentication changes
    if (authStore.isAuthenticated) {
      updateUserProperties()
    }
  })

  onUnmounted(() => {
    if (isTracking.value) {
      trackPageTime()
    }
  })

  // Watch for route changes (if router is available) - DISABLED to fix CPU
  // if (router && typeof router.afterEach === 'function') {
  //   router.afterEach(() => {
  //     trackPageTime() // Track time on previous page
  //     pageStartTime.value = Date.now() // Reset timer for new page
  //     setTimeout(trackCurrentPage, 100) // Track new page after DOM update
  //   })
  // }

  return {
    ...analytics,
    isTracking,
    pageStartTime
  }
}

// Global analytics instance for use outside components
export const globalAnalytics = {
  trackEvent,
  trackPageView,
  trackUserAction,
  trackEnergyConsultation,
  trackContactForm,
  trackServiceInterest,
  trackQuoteRequest,
  trackUserEngagement
}
