import { onMounted, onUnmounted } from 'vue'

/**
 * Composable for handling data refresh after session unlock
 * Components can use this to automatically refresh their data when session is unlocked
 */
export function useSessionUnlockRefresh(refreshCallback?: () => void | Promise<void>) {
  let sessionUnlockedListener: ((event: CustomEvent) => void) | null = null
  let adminRefreshListener: (() => void) | null = null
  let dashboardRefreshListener: (() => void) | null = null

  const handleSessionUnlocked = async (event: CustomEvent) => {
    console.log('🔓 Session unlocked event received:', event.detail)
    
    if (refreshCallback) {
      try {
        console.log('🔄 Executing refresh callback...')
        await refreshCallback()
        console.log('✅ Refresh callback completed')
      } catch (error) {
        console.error('❌ Error in refresh callback:', error)
      }
    }
  }

  const handleAdminRefresh = async () => {
    console.log('🔧 Admin data refresh event received')
    
    if (refreshCallback) {
      try {
        console.log('🔄 Executing admin refresh callback...')
        await refreshCallback()
        console.log('✅ Admin refresh callback completed')
      } catch (error) {
        console.error('❌ Error in admin refresh callback:', error)
      }
    }
  }

  const handleDashboardRefresh = async () => {
    console.log('📊 Dashboard data refresh event received')
    
    if (refreshCallback) {
      try {
        console.log('🔄 Executing dashboard refresh callback...')
        await refreshCallback()
        console.log('✅ Dashboard refresh callback completed')
      } catch (error) {
        console.error('❌ Error in dashboard refresh callback:', error)
      }
    }
  }

  const setupListeners = () => {
    // General session unlock listener
    sessionUnlockedListener = handleSessionUnlocked
    window.addEventListener('session-unlocked', sessionUnlockedListener as EventListener)

    // Route-specific listeners
    adminRefreshListener = handleAdminRefresh
    window.addEventListener('admin-data-refresh', adminRefreshListener)

    dashboardRefreshListener = handleDashboardRefresh
    window.addEventListener('dashboard-data-refresh', dashboardRefreshListener)

    console.log('🎧 Session unlock refresh listeners set up')
  }

  const cleanupListeners = () => {
    if (sessionUnlockedListener) {
      window.removeEventListener('session-unlocked', sessionUnlockedListener as EventListener)
      sessionUnlockedListener = null
    }

    if (adminRefreshListener) {
      window.removeEventListener('admin-data-refresh', adminRefreshListener)
      adminRefreshListener = null
    }

    if (dashboardRefreshListener) {
      window.removeEventListener('dashboard-data-refresh', dashboardRefreshListener)
      dashboardRefreshListener = null
    }

    console.log('🧹 Session unlock refresh listeners cleaned up')
  }

  onMounted(() => {
    setupListeners()
  })

  onUnmounted(() => {
    cleanupListeners()
  })

  // Return methods for manual control if needed
  return {
    setupListeners,
    cleanupListeners,
    // Trigger refresh manually
    triggerRefresh: refreshCallback
  }
}

/**
 * Composable for page refresh after session unlock
 * Use this when you want to completely reload the page after unlock
 */
export function usePageRefreshOnUnlock(delay: number = 1000) {
  const refreshPage = () => {
    console.log(`🔄 Page will refresh in ${delay}ms after session unlock...`)
    setTimeout(() => {
      console.log('🔄 Refreshing page after session unlock...')
      window.location.reload()
    }, delay)
  }

  return useSessionUnlockRefresh(refreshPage)
}

/**
 * Composable for specific data refresh after session unlock
 * Use this when you want to refresh specific data without full page reload
 */
export function useDataRefreshOnUnlock(refreshFunction: () => void | Promise<void>) {
  return useSessionUnlockRefresh(refreshFunction)
}

/**
 * Manual trigger for session unlock refresh
 * Use this to manually trigger the refresh events (useful for testing)
 */
export function triggerSessionUnlockRefresh() {
  console.log('🔄 Manually triggering session unlock refresh...')
  
  window.dispatchEvent(new CustomEvent('session-unlocked', {
    detail: {
      timestamp: new Date().toISOString(),
      manual: true
    }
  }))

  // Also trigger route-specific events based on current path
  const currentRoute = window.location.pathname
  
  if (currentRoute.includes('/admin')) {
    window.dispatchEvent(new CustomEvent('admin-data-refresh'))
  } else if (currentRoute.includes('/dashboard')) {
    window.dispatchEvent(new CustomEvent('dashboard-data-refresh'))
  }
}
