import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createHead } from '@vueuse/head'

import App from './App.vue'
import router from './router'
import i18n from './i18n'
import { useAuthStore } from './stores/auth'

// Import Socket.io
import socketPlugin from './plugins/socket'
import { useSocket } from './composables/useSocket'

// Import analytics
// import { analytics } from './services/analytics'

// Initialize Firebase Analytics
import './plugins/firebase'

const app = createApp(App)
const pinia = createPinia()
const head = createHead()

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(head)
app.use(socketPlugin)

// Initialize auth state and mount app
const initializeApp = async () => {
  const authStore = useAuthStore()

  // Initialize auth state before mounting
  try {
    await authStore.initializeAuth()
  } catch (error) {
    console.warn('Auth initialization failed:', error)
  }

  // Initialize Socket.io and analytics
  const socket = useSocket()
  // analytics.initialize({
  //   debug: import.meta.env.DEV,
  //   socket
  // })

  // Set socket in analytics service
  // analytics.setSocket(socket)

  app.mount('#app')
}

// Start the app
initializeApp()

// Import PWA test utilities in development
if (import.meta.env.DEV) {
  import('@/utils/pwaTest')
  import('@/utils/testPWAUpdate')
}
