// Firebase configuration and initialization
import { initializeApp } from 'firebase/app'
import { getAnalytics, logEvent, setUserProperties, setUserId } from 'firebase/analytics'
import { getMessaging } from 'firebase/messaging'

// Your web app's Firebase configuration
// TODO: Replace with your actual Firebase config from Firebase Console
const firebaseConfig = {
  apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
  authDomain: "hlenergy-notifications.firebaseapp.com",
  projectId: "hlenergy-notifications",
  storageBucket: "hlenergy-notifications.firebasestorage.app",
  messagingSenderId: "506206785168",
  appId: "1:506206785168:web:5acaeacce5178fd2d45215",
  measurementId: "G-JEMVPQGQ5R"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Analytics
let analytics = null
let messaging = null

// Check if running in browser (not SSR)
if (typeof window !== 'undefined') {
  try {
    analytics = getAnalytics(app)
    messaging = getMessaging(app)
    console.log('🔥 Firebase Analytics initialized')
  } catch (error) {
    console.warn('Firebase Analytics not available:', error)
  }
}

// Analytics throttling to prevent CPU spikes
let lastAnalyticsEvent = 0
const ANALYTICS_THROTTLE = 1000 // Max 1 event per second
const eventQueue = new Map()

// Analytics helper functions
export const trackEvent = (eventName, parameters = {}) => {
  if (!analytics) return

  const now = Date.now()

  // Throttle events to prevent CPU spikes
  if (now - lastAnalyticsEvent < ANALYTICS_THROTTLE) {
    // Queue the event instead of dropping it
    eventQueue.set(eventName, { eventName, parameters, timestamp: now })
    return
  }

  lastAnalyticsEvent = now

  try {
    logEvent(analytics, eventName, parameters)
    // Remove console.log to reduce overhead
  } catch (error) {
    console.warn('Analytics error:', error)
  }

  // Process queued events (max 3 per throttle window)
  let processed = 0
  for (const [key, event] of eventQueue.entries()) {
    if (processed >= 3) break
    try {
      logEvent(analytics, event.eventName, event.parameters)
      eventQueue.delete(key)
      processed++
    } catch (error) {
      console.warn('Queued analytics error:', error)
    }
  }
}

export const trackPageView = (pageName, pageTitle = '') => {
  if (analytics) {
    logEvent(analytics, 'page_view', {
      page_title: pageTitle || pageName,
      page_location: window.location.href,
      page_path: window.location.pathname
    })
  }
}

export const trackUserAction = (action, category = 'engagement', label = '') => {
  if (analytics) {
    logEvent(analytics, action, {
      event_category: category,
      event_label: label,
      value: 1
    })
  }
}

export const setAnalyticsUser = (userId, properties = {}) => {
  if (analytics) {
    setUserId(analytics, userId)
    setUserProperties(analytics, properties)
  }
}

// Business-specific tracking for HLenergy
export const trackEnergyConsultation = (consultationType, serviceType) => {
  trackEvent('energy_consultation_request', {
    consultation_type: consultationType, // 'b2b' or 'b2c'
    service_type: serviceType, // 'audit', 'optimization', 'renewable', etc.
    timestamp: new Date().toISOString()
  })
}

export const trackContactForm = (formType, source = '') => {
  trackEvent('contact_form_submission', {
    form_type: formType, // 'contact', 'quote', 'consultation'
    source: source, // 'homepage', 'services', 'about'
    timestamp: new Date().toISOString()
  })
}

export const trackServiceInterest = (serviceName, pageTime = 0) => {
  trackEvent('service_interest', {
    service_name: serviceName,
    page_time_seconds: pageTime,
    timestamp: new Date().toISOString()
  })
}

export const trackQuoteRequest = (serviceType, estimatedValue = 0) => {
  trackEvent('quote_request', {
    service_type: serviceType,
    estimated_value: estimatedValue,
    currency: 'EUR',
    timestamp: new Date().toISOString()
  })
}

export const trackUserEngagement = (action, element = '') => {
  trackEvent('user_engagement', {
    engagement_type: action, // 'click', 'scroll', 'download', 'video_play'
    element: element,
    timestamp: new Date().toISOString()
  })
}

// Export Firebase instances
export { app, analytics, messaging }
export default { trackEvent, trackPageView, trackUserAction, setAnalyticsUser }
