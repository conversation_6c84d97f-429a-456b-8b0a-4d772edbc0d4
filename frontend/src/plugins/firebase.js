// Firebase configuration - LAZY LOADED for performance
const firebaseConfig = {
  apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
  authDomain: "hlenergy-notifications.firebaseapp.com",
  projectId: "hlenergy-notifications",
  storageBucket: "hlenergy-notifications.firebasestorage.app",
  messagingSenderId: "506206785168",
  appId: "1:506206785168:web:5acaeacce5178fd2d45215",
  measurementId: "G-JEMVPQGQ5R"
};

// Lazy initialization - only load when needed
let app = null
let analytics = null
let messaging = null
let isInitializing = false
let initPromise = null

// Lazy initialize Firebase
const initializeFirebase = async () => {
  if (app) return app
  if (isInitializing) return initPromise

  isInitializing = true
  initPromise = (async () => {
    try {
      // Dynamic import to avoid blocking main thread
      const { initializeApp } = await import('firebase/app')
      app = initializeApp(firebaseConfig)
      console.log('🔥 Firebase app initialized (lazy)')
      return app
    } catch (error) {
      console.warn('Firebase initialization failed:', error)
      throw error
    } finally {
      isInitializing = false
    }
  })()

  return initPromise
}

// Lazy initialize Analytics
const initializeAnalytics = async () => {
  if (analytics) return analytics
  if (typeof window === 'undefined') return null

  try {
    await initializeFirebase()
    const { getAnalytics } = await import('firebase/analytics')
    analytics = getAnalytics(app)
    console.log('� Firebase Analytics initialized (lazy)')
    return analytics
  } catch (error) {
    console.warn('Firebase Analytics initialization failed:', error)
    return null
  }
}

// Analytics throttling to prevent CPU spikes
let lastAnalyticsEvent = 0
const ANALYTICS_THROTTLE = 1000 // Max 1 event per second
const eventQueue = new Map()
const MAX_QUEUE_SIZE = 50 // Prevent memory leaks
const QUEUE_CLEANUP_INTERVAL = 30000 // Clean old events every 30 seconds

// Periodic cleanup of old events to prevent memory leaks
setInterval(() => {
  const now = Date.now()
  const oldEventThreshold = now - 60000 // Remove events older than 1 minute

  for (const [key, event] of eventQueue.entries()) {
    if (event.timestamp < oldEventThreshold) {
      eventQueue.delete(key)
    }
  }

  // If queue is still too large, remove oldest events
  if (eventQueue.size > MAX_QUEUE_SIZE) {
    const sortedEvents = Array.from(eventQueue.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp)

    const toRemove = sortedEvents.slice(0, eventQueue.size - MAX_QUEUE_SIZE)
    toRemove.forEach(([key]) => eventQueue.delete(key))

    console.warn(`🧹 Firebase: Cleaned up ${toRemove.length} old events from queue`)
  }
}, QUEUE_CLEANUP_INTERVAL)

// Analytics helper functions - LAZY LOADED
export const trackEvent = async (eventName, parameters = {}) => {
  const now = Date.now()

  // Throttle events to prevent CPU spikes
  if (now - lastAnalyticsEvent < ANALYTICS_THROTTLE) {
    // Queue the event instead of dropping it
    eventQueue.set(eventName, { eventName, parameters, timestamp: now })
    return
  }

  lastAnalyticsEvent = now

  try {
    // Lazy load analytics
    const analyticsInstance = await initializeAnalytics()
    if (!analyticsInstance) return

    // Dynamic import to avoid blocking
    const { logEvent } = await import('firebase/analytics')
    logEvent(analyticsInstance, eventName, parameters)
  } catch (error) {
    console.warn('Analytics error:', error)
  }

  // Process queued events (max 3 per throttle window)
  let processed = 0
  for (const [key, event] of eventQueue.entries()) {
    if (processed >= 3) break
    try {
      const analyticsInstance = await initializeAnalytics()
      if (analyticsInstance) {
        const { logEvent } = await import('firebase/analytics')
        logEvent(analyticsInstance, event.eventName, event.parameters)
        eventQueue.delete(key)
        processed++
      }
    } catch (error) {
      console.warn('Queued analytics error:', error)
    }
  }
}

export const trackPageView = async (pageName, pageTitle = '') => {
  try {
    const analyticsInstance = await initializeAnalytics()
    if (!analyticsInstance) return

    const { logEvent } = await import('firebase/analytics')
    logEvent(analyticsInstance, 'page_view', {
      page_title: pageTitle || pageName,
      page_location: window.location.href,
      page_path: window.location.pathname
    })
  } catch (error) {
    console.warn('Track page view error:', error)
  }
}

export const trackUserAction = async (action, category = 'engagement', label = '') => {
  try {
    const analyticsInstance = await initializeAnalytics()
    if (!analyticsInstance) return

    const { logEvent } = await import('firebase/analytics')
    logEvent(analyticsInstance, action, {
      event_category: category,
      event_label: label,
      value: 1
    })
  } catch (error) {
    console.warn('Track user action error:', error)
  }
}

export const setAnalyticsUser = async (userId, properties = {}) => {
  try {
    const analyticsInstance = await initializeAnalytics()
    if (!analyticsInstance) return

    const { setUserId, setUserProperties } = await import('firebase/analytics')
    setUserId(analyticsInstance, userId)
    setUserProperties(analyticsInstance, properties)
  } catch (error) {
    console.warn('Set analytics user error:', error)
  }
}

// Business-specific tracking for HLenergy
export const trackEnergyConsultation = (consultationType, serviceType) => {
  trackEvent('energy_consultation_request', {
    consultation_type: consultationType, // 'b2b' or 'b2c'
    service_type: serviceType, // 'audit', 'optimization', 'renewable', etc.
    timestamp: new Date().toISOString()
  })
}

export const trackContactForm = (formType, source = '') => {
  trackEvent('contact_form_submission', {
    form_type: formType, // 'contact', 'quote', 'consultation'
    source: source, // 'homepage', 'services', 'about'
    timestamp: new Date().toISOString()
  })
}

export const trackServiceInterest = (serviceName, pageTime = 0) => {
  trackEvent('service_interest', {
    service_name: serviceName,
    page_time_seconds: pageTime,
    timestamp: new Date().toISOString()
  })
}

export const trackQuoteRequest = (serviceType, estimatedValue = 0) => {
  trackEvent('quote_request', {
    service_type: serviceType,
    estimated_value: estimatedValue,
    currency: 'EUR',
    timestamp: new Date().toISOString()
  })
}

export const trackUserEngagement = (action, element = '') => {
  trackEvent('user_engagement', {
    engagement_type: action, // 'click', 'scroll', 'download', 'video_play'
    element: element,
    timestamp: new Date().toISOString()
  })
}

// Export Firebase instances
export { app, analytics, messaging }
export default { trackEvent, trackPageView, trackUserAction, setAnalyticsUser }
