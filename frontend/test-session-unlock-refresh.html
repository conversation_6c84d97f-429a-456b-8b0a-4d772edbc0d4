<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Unlock Refresh Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        button.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
        .refresh-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .refresh-option {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .refresh-option:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .refresh-option.selected {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .refresh-option h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .refresh-option p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Session Unlock Refresh Test</h1>
    <p>This test demonstrates the new refresh functionality after session unlock.</p>

    <div class="test-section">
        <h2>Refresh Options</h2>
        <p>Choose what happens after session unlock:</p>
        
        <div class="refresh-options">
            <div class="refresh-option" data-option="data" onclick="selectRefreshOption('data')">
                <h3>🔄 Refresh Data</h3>
                <p>Updates component data without page reload. Fastest option.</p>
            </div>
            <div class="refresh-option" data-option="page" onclick="selectRefreshOption('page')">
                <h3>🔄 Refresh Page</h3>
                <p>Completely reloads the page. Most thorough option.</p>
            </div>
            <div class="refresh-option" data-option="none" onclick="selectRefreshOption('none')">
                <h3>⏸️ No Refresh</h3>
                <p>No automatic refresh. Manual control only.</p>
            </div>
        </div>
        
        <p><strong>Selected:</strong> <span id="selectedOption">data</span></p>
    </div>

    <div class="test-section">
        <h2>Test Actions</h2>
        <button onclick="simulateSessionUnlock()">🔓 Simulate Session Unlock</button>
        <button onclick="triggerDataRefresh()" class="success">🔄 Trigger Data Refresh</button>
        <button onclick="triggerPageRefresh()" class="warning">🔄 Trigger Page Refresh</button>
        <button onclick="testEventListeners()">🎧 Test Event Listeners</button>
        <button onclick="clearLog()" class="danger">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Event Listeners</h2>
        <p>Active listeners for session unlock events:</p>
        <ul>
            <li><code>session-unlocked</code> - General unlock event</li>
            <li><code>admin-data-refresh</code> - Admin-specific refresh</li>
            <li><code>dashboard-data-refresh</code> - Dashboard-specific refresh</li>
            <li><code>session-unlock-refresh</code> - Custom refresh event</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>How It Works</h2>
        <ol>
            <li><strong>User locks session</strong> (manually or timeout)</li>
            <li><strong>User unlocks session</strong> (PIN or biometric)</li>
            <li><strong>Auth store refreshes data</strong> (user profile, etc.)</li>
            <li><strong>Events are emitted</strong> for components to refresh</li>
            <li><strong>Components respond</strong> based on their refresh logic</li>
            <li><strong>User preference</strong> determines refresh behavior</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        let selectedRefreshOption = 'data';

        // Logging function
        function log(level, message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Refresh option selection
        function selectRefreshOption(option) {
            selectedRefreshOption = option;
            document.getElementById('selectedOption').textContent = option;
            
            // Update visual selection
            document.querySelectorAll('.refresh-option').forEach(el => {
                el.classList.remove('selected');
            });
            document.querySelector(`[data-option="${option}"]`).classList.add('selected');
            
            log('info', `Selected refresh option: ${option}`);
        }

        // Test functions
        function simulateSessionUnlock() {
            log('info', '🔓 Simulating session unlock...');
            
            // Simulate the auth store refresh process
            log('info', '1. Auth store refreshing user profile...');
            setTimeout(() => {
                log('success', '✅ User profile refreshed');
                
                log('info', '2. Emitting session-unlocked event...');
                window.dispatchEvent(new CustomEvent('session-unlocked', {
                    detail: {
                        timestamp: new Date().toISOString(),
                        userId: 'test-user-123'
                    }
                }));
                
                log('info', '3. Emitting route-specific events...');
                const currentPath = window.location.pathname;
                if (currentPath.includes('admin')) {
                    window.dispatchEvent(new CustomEvent('admin-data-refresh'));
                    log('info', '📡 admin-data-refresh event emitted');
                } else {
                    window.dispatchEvent(new CustomEvent('dashboard-data-refresh'));
                    log('info', '📡 dashboard-data-refresh event emitted');
                }
                
                log('info', '4. Handling refresh option...');
                handleRefreshOption();
                
            }, 1000);
        }

        function handleRefreshOption() {
            switch (selectedRefreshOption) {
                case 'page':
                    log('warning', '🔄 Page refresh requested - reloading in 2 seconds...');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                    break;
                    
                case 'data':
                    log('success', '🔄 Data refresh requested - emitting custom event...');
                    window.dispatchEvent(new CustomEvent('session-unlock-refresh', {
                        detail: { 
                            option: 'data',
                            timestamp: new Date().toISOString()
                        }
                    }));
                    break;
                    
                case 'none':
                    log('info', '⏸️ No refresh requested');
                    break;
            }
        }

        function triggerDataRefresh() {
            log('success', '🔄 Manually triggering data refresh...');
            window.dispatchEvent(new CustomEvent('session-unlock-refresh', {
                detail: { 
                    option: 'data',
                    manual: true,
                    timestamp: new Date().toISOString()
                }
            }));
        }

        function triggerPageRefresh() {
            log('warning', '🔄 Manually triggering page refresh...');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        function testEventListeners() {
            log('info', '🎧 Testing all event listeners...');
            
            const events = [
                'session-unlocked',
                'admin-data-refresh', 
                'dashboard-data-refresh',
                'session-unlock-refresh'
            ];
            
            events.forEach((eventName, index) => {
                setTimeout(() => {
                    log('info', `📡 Emitting ${eventName}...`);
                    window.dispatchEvent(new CustomEvent(eventName, {
                        detail: { test: true, eventName }
                    }));
                }, index * 500);
            });
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('info', 'Test log cleared');
        }

        // Set up event listeners
        function setupEventListeners() {
            window.addEventListener('session-unlocked', (event) => {
                log('success', `🔓 session-unlocked event received: ${JSON.stringify(event.detail)}`);
            });

            window.addEventListener('admin-data-refresh', () => {
                log('success', '🔧 admin-data-refresh event received');
            });

            window.addEventListener('dashboard-data-refresh', () => {
                log('success', '📊 dashboard-data-refresh event received');
            });

            window.addEventListener('session-unlock-refresh', (event) => {
                log('success', `🔄 session-unlock-refresh event received: ${JSON.stringify(event.detail)}`);
            });

            log('info', '🎧 Event listeners set up');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('info', 'Session Unlock Refresh Test initialized');
            selectRefreshOption('data'); // Set default
            setupEventListeners();
        });
    </script>
</body>
</html>
